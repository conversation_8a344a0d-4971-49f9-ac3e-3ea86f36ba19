<?php
/**
 * My Courses Page - Market eklentisi için <PERSON>'dan <PERSON>
 *
 * @package MarketKing
 * @subpackage Dashboard
 * @since 1.0.0
 */

if (!defined('ABSPATH')) { exit; }

// Kurslar artık products sayfasında gösteriliyor, oraya yönlendir
$products_page_url = trailingslashit(get_page_link(get_option('marketking_vendordash_page_setting', 'disabled'))) . 'products';
wp_redirect($products_page_url);
exit;



// Tutor eklentisinin yüklü olup olmadığını kontrol et
if (!class_exists('TUTOR\Tutor')) {
    ?>
    <div class="nk-content ">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="nk-block-head nk-block-head-sm">
                        <div class="nk-block-between">
                            <div class="nk-block-head-content">
                                <h3 class="nk-block-title page-title"><?php esc_html_e('Kurslarım', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h3>
                            </div>
                        </div>
                    </div>
                    <div class="nk-block">
                        <div class="card">
                            <div class="card-inner">
                                <div class="alert alert-warning">
                                    <div class="alert-cta">
                                        <h4><?php esc_html_e('Tutor LMS Gerekli', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                                        <p><?php esc_html_e('Kurslarım özelliğini kullanabilmek için Tutor LMS eklentisinin yüklü ve aktif olması gerekiyor.', 'marketking-multivendor-marketplace-for-woocommerce'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    return;
}

// Tutor fonksiyonlarını kullan
use Tutor\Models\CourseModel;

// Get the user ID and active tab.
$current_user_id = get_current_user_id();
$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'my-courses';

// Map required course status according to page.
$status_map = array(
    'my-courses'                  => CourseModel::STATUS_PUBLISH,
    'draft-courses'               => CourseModel::STATUS_DRAFT,
    'pending-courses'             => CourseModel::STATUS_PENDING,
);

// Set currently required course status for current tab.
$status = isset($status_map[$active_tab]) ? $status_map[$active_tab] : CourseModel::STATUS_PUBLISH;
$post_type = apply_filters('tutor_dashboard_course_list_post_type', array(tutor()->course_post_type));

// Get counts for course tabs.
$count_map = array(
    'publish' => CourseModel::get_courses_by_instructor($current_user_id, CourseModel::STATUS_PUBLISH, 0, 0, true, $post_type),
    'pending' => CourseModel::get_courses_by_instructor($current_user_id, CourseModel::STATUS_PENDING, 0, 0, true, $post_type),
    'draft'   => CourseModel::get_courses_by_instructor($current_user_id, CourseModel::STATUS_DRAFT, 0, 0, true, $post_type),
);

$per_page = 10;
$paged = isset($_GET['current_page']) ? intval($_GET['current_page']) : 1;
$offset = $per_page * ($paged - 1);
$results = CourseModel::get_courses_by_instructor($current_user_id, $status, $offset, $per_page, false, $post_type);

$tabs = array(
    'publish' => array(
        'title' => __('Yayınlanan', 'marketking-multivendor-marketplace-for-woocommerce'),
        'link'  => 'my-courses',
    ),
    'pending' => array(
        'title' => __('Bekleyen', 'marketking-multivendor-marketplace-for-woocommerce'),
        'link'  => 'pending-courses',
    ),
    'draft'   => array(
        'title' => __('Taslak', 'marketking-multivendor-marketplace-for-woocommerce'),
        'link'  => 'draft-courses',
    ),
);

$dashboard_url = trailingslashit(get_page_link(apply_filters('wpml_object_id', get_option('marketking_vendordash_page_setting', 'disabled'), 'post', true)));
?>

<style>
.course-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-image-container {
    overflow: hidden;
    height: 200px;
}

.course-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.course-card:hover .course-thumbnail {
    transform: scale(1.05);
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.course-card:hover .card-overlay {
    opacity: 1;
}

.overlay-actions .btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 0 0.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    cursor: pointer;
    z-index: 10;
}

/* Silme butonu özel stilleri */
.delete-course-btn {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

.delete-course-btn:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
    transform: scale(1.1);
}

.course-title {
    font-size: 1.1rem;
    line-height: 1.4;
    height: 2.8rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.course-title a:hover {
    color: #6366f1 !important;
}

.stat-item {
    padding: 0.5rem;
    border-radius: 8px;
    background: rgba(99, 102, 241, 0.05);
}

.stat-item .icon {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

.badge-pill {
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 500;
}

.card-footer {
    padding: 1rem;
    background: #f8f9fa !important;
}

.course-price .fw-bold {
    font-size: 1.1rem;
}

.empty-state {
    padding: 3rem 1rem;
}

.empty-state-icon {
    opacity: 0.3;
}

.empty-state-title {
    color: #374151;
    font-weight: 600;
}

.empty-state-text {
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
}



.pagination-modern .page-link {
    border-radius: 8px;
    margin: 0 0.25rem;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e5e7eb;
    color: #6b7280;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination-modern .page-link:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
    color: #374151;
    transform: translateY(-1px);
}

.pagination-modern .page-item.active .page-link {
    background: #6366f1;
    border-color: #6366f1;
    color: white;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.pagination-modern .page-item.disabled .page-link {
    background: #f9fafb;
    border-color: #e5e7eb;
    color: #9ca3af;
}

@media (max-width: 768px) {
    .course-card {
        margin-bottom: 1rem;
    }

    .card-image-container {
        height: 180px;
    }

    .overlay-actions .btn {
        width: 35px;
        height: 35px;
    }



    .pagination-modern .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }

    .stat-item {
        padding: 0.375rem;
    }

    .empty-state {
        padding: 2rem 1rem;
    }

    .empty-state-icon em {
        font-size: 3rem !important;
    }
}
</style>

<div class="nk-content ">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title"><?php esc_html_e('Kurslarım', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h3>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card">
                        <div class="card-inner">
                            <!-- Tab Navigation -->
                            <ul class="nav nav-tabs nav-tabs-mb-icon nav-tabs-card">
                                <?php foreach ($tabs as $key => $tab) : ?>
                                <li class="nav-item">
                                    <a class="nav-link<?php echo esc_attr($tab['link'] === $active_tab ? ' active' : ''); ?>"
                                       href="<?php echo esc_url($dashboard_url . 'my-courses?tab=' . $tab['link']); ?>">
                                        <?php echo esc_html($tab['title']); ?>
                                        <span class="badge badge-gray"><?php echo esc_html($count_map[$key]); ?></span>
                                    </a>
                                </li>
                                <?php endforeach; ?>
                            </ul>

                            <!-- Course List -->
                            <div class="tab-content">
                                <div class="tab-pane active">
                                    <?php
                                    if (!is_array($results) || (!count($results) && 1 == $paged)) {
                                        ?>
                                        <div class="empty-state text-center py-5">
                                            <div class="empty-state-icon mb-4">
                                                <em class="icon ni ni-book-read" style="font-size: 4rem; color: #e5e7eb;"></em>
                                            </div>
                                            <h4 class="empty-state-title mb-2"><?php esc_html_e('Henüz kurs bulunamadı', 'marketking-multivendor-marketplace-for-woocommerce'); ?></h4>
                                            <p class="empty-state-text text-muted mb-4">
                                                <?php
                                                if ($active_tab === 'my-courses') {
                                                    esc_html_e('Henüz yayınlanmış kursunuz bulunmuyor. İlk kursunuzu oluşturmaya başlayın!', 'marketking-multivendor-marketplace-for-woocommerce');
                                                } elseif ($active_tab === 'draft-courses') {
                                                    esc_html_e('Taslak halinde kursunuz bulunmuyor.', 'marketking-multivendor-marketplace-for-woocommerce');
                                                } elseif ($active_tab === 'pending-courses') {
                                                    esc_html_e('Onay bekleyen kursunuz bulunmuyor.', 'marketking-multivendor-marketplace-for-woocommerce');
                                                } else {
                                                    esc_html_e('Bu kategoride henüz hiç kursunuz bulunmuyor.', 'marketking-multivendor-marketplace-for-woocommerce');
                                                }
                                                ?>
                                            </p>
                                            <?php if (current_user_can(tutor()->instructor_role)) : ?>
                                            <a href="<?php echo esc_url(admin_url('post-new.php?post_type=' . tutor()->course_post_type)); ?>" class="btn btn-primary">
                                                <em class="icon ni ni-plus"></em>
                                                <?php esc_html_e('Yeni Kurs Oluştur', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                        <?php
                                    } else {
                                        ?>
                                        <div class="row gy-4">
                                            <?php
                                            global $post;
                                            foreach ($results as $post) :
                                                setup_postdata($post);

                                                $course_thumbnail = get_tutor_course_thumbnail_src();
                                                $placeholder_img = tutor()->url . 'assets/images/placeholder.svg';
                                                $course_img = empty($course_thumbnail) ? $placeholder_img : $course_thumbnail;
                                                $course_duration = get_tutor_course_duration_context($post->ID, true);
                                                $course_students = tutor_utils()->count_enrolled_users_by_course();
                                                $is_main_instructor = CourseModel::is_main_instructor($post->ID);
                                                $course_edit_link = tutor_utils()->course_edit_link($post->ID, 'backend');
                                                $course_rating = tutor_utils()->get_course_rating()->rating_avg;
                                                $course_rating_count = tutor_utils()->get_course_rating()->rating_count;

                                                // Kurs durumu badge'i için renk belirleme
                                                $status_badge_class = '';
                                                $status_text = '';
                                                switch ($post->post_status) {
                                                    case 'publish':
                                                        $status_badge_class = 'badge-success';
                                                        $status_text = __('Yayında', 'marketking-multivendor-marketplace-for-woocommerce');
                                                        break;
                                                    case 'pending':
                                                        $status_badge_class = 'badge-warning';
                                                        $status_text = __('Onay Bekliyor', 'marketking-multivendor-marketplace-for-woocommerce');
                                                        break;
                                                    case 'draft':
                                                        $status_badge_class = 'badge-gray';
                                                        $status_text = __('Taslak', 'marketking-multivendor-marketplace-for-woocommerce');
                                                        break;
                                                }
                                                ?>

                                                <div class="col-xl-3 col-lg-4 col-md-6 col-sm-12">
                                                    <div id="tutor-dashboard-my-course-<?php echo esc_attr($post->ID); ?>" class="card card-bordered h-100 course-card">
                                                        <!-- Kurs Görseli -->
                                                        <div class="card-image-container position-relative">
                                                            <img src="<?php echo esc_url($course_img); ?>" alt="<?php the_title(); ?>" class="card-img-top course-thumbnail">

                                                            <!-- Durum Badge'i -->
                                                            <div class="position-absolute top-0 start-0 m-2">
                                                                <span class="badge <?php echo esc_attr($status_badge_class); ?> badge-pill">
                                                                    <?php echo esc_html($status_text); ?>
                                                                </span>
                                                            </div>

                                                            <!-- Ortak Yazar Badge'i -->
                                                            <?php if (false === $is_main_instructor) : ?>
                                                            <div class="position-absolute top-0 end-0 m-2">
                                                                <span class="badge badge-primary badge-pill">
                                                                    <?php esc_html_e('Ortak Yazar', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                                                                </span>
                                                            </div>
                                                            <?php endif; ?>

                                                            <!-- Hover Overlay -->
                                                            <div class="card-overlay">
                                                                <div class="overlay-actions">
                                                                    <a href="<?php echo esc_url($course_edit_link); ?>" class="btn btn-white btn-sm me-2" title="<?php esc_attr_e('Düzenle', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
                                                                        <em class="icon ni ni-edit"></em>
                                                                    </a>
                                                                    <a href="<?php echo esc_url(get_the_permalink()); ?>" target="_blank" class="btn btn-white btn-sm me-2" title="<?php esc_attr_e('Görüntüle', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
                                                                        <em class="icon ni ni-eye"></em>
                                                                    </a>
                                                                    <a href="#" class="btn btn-danger btn-sm marketking_delete_course_button" value="<?php echo esc_attr($post->ID); ?>" title="<?php esc_attr_e('Sil', 'marketking-multivendor-marketplace-for-woocommerce'); ?>">
                                                                        <em class="icon ni ni-trash"></em>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Kart İçeriği -->
                                                        <div class="card-inner">
                                                            <!-- Kurs Başlığı -->
                                                            <h5 class="card-title course-title mb-2">
                                                                <a href="<?php echo esc_url(get_the_permalink()); ?>" class="text-dark text-decoration-none">
                                                                    <?php the_title(); ?>
                                                                </a>
                                                            </h5>

                                                            <!-- Kurs Açıklaması -->
                                                            <p class="card-text text-muted small mb-3">
                                                                <?php
                                                                $excerpt = get_the_excerpt();
                                                                echo esc_html(wp_trim_words($excerpt, 15, '...'));
                                                                ?>
                                                            </p>

                                                            <!-- Kurs İstatistikleri -->
                                                            <div class="course-stats mb-3">
                                                                <div class="row g-2 text-center">
                                                                    <?php if (!empty($course_students)) : ?>
                                                                    <div class="col-4">
                                                                        <div class="stat-item">
                                                                            <em class="icon ni ni-users text-primary"></em>
                                                                            <small class="d-block text-muted"><?php echo wp_kses_post($course_students); ?></small>
                                                                            <small class="text-muted"><?php esc_html_e('Öğrenci', 'marketking-multivendor-marketplace-for-woocommerce'); ?></small>
                                                                        </div>
                                                                    </div>
                                                                    <?php endif; ?>

                                                                    <?php if (!empty($course_duration)) : ?>
                                                                    <div class="col-4">
                                                                        <div class="stat-item">
                                                                            <em class="icon ni ni-clock text-info"></em>
                                                                            <small class="d-block text-muted"><?php echo wp_kses_post($course_duration); ?></small>
                                                                            <small class="text-muted"><?php esc_html_e('Süre', 'marketking-multivendor-marketplace-for-woocommerce'); ?></small>
                                                                        </div>
                                                                    </div>
                                                                    <?php endif; ?>

                                                                    <?php if ($course_rating > 0) : ?>
                                                                    <div class="col-4">
                                                                        <div class="stat-item">
                                                                            <em class="icon ni ni-star-fill text-warning"></em>
                                                                            <small class="d-block text-muted"><?php echo esc_html(number_format($course_rating, 1)); ?></small>
                                                                            <small class="text-muted">(<?php echo esc_html($course_rating_count); ?>)</small>
                                                                        </div>
                                                                    </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Kart Alt Kısmı -->
                                                        <div class="card-footer bg-light border-top-0">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <!-- Fiyat -->
                                                                <div class="course-price">
                                                                    <?php
                                                                    $price = tutor_utils()->get_course_price();
                                                                    if (null === $price) {
                                                                        echo '<span class="badge badge-success badge-pill">' . esc_html__('Ücretsiz', 'marketking-multivendor-marketplace-for-woocommerce') . '</span>';
                                                                    } else {
                                                                        echo '<span class="fw-bold text-primary">' . wp_kses_post($price) . '</span>';
                                                                    }
                                                                    ?>
                                                                </div>

                                                                <!-- Tarih -->
                                                                <small class="text-muted">
                                                                    <?php echo esc_html(get_the_date('d M Y')); ?>
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <?php
                                            endforeach;
                                            wp_reset_postdata();
                                            ?>
                                        </div>

                                        <!-- Pagination -->
                                        <?php if ($count_map[$status] > $per_page) : ?>
                                        <div class="mt-5">
                                            <?php
                                            $total_pages = ceil($count_map[$status] / $per_page);
                                            if ($total_pages > 1) {
                                                echo '<nav aria-label="Page navigation" class="d-flex justify-content-center">';
                                                echo '<ul class="pagination pagination-modern">';

                                                // Previous button
                                                if ($paged > 1) {
                                                    echo '<li class="page-item"><a class="page-link" href="' . esc_url($dashboard_url . 'my-courses?tab=' . $active_tab . '&current_page=' . ($paged - 1)) . '"><em class="icon ni ni-chevron-left"></em> ' . esc_html__('Önceki', 'marketking-multivendor-marketplace-for-woocommerce') . '</a></li>';
                                                } else {
                                                    echo '<li class="page-item disabled"><span class="page-link"><em class="icon ni ni-chevron-left"></em> ' . esc_html__('Önceki', 'marketking-multivendor-marketplace-for-woocommerce') . '</span></li>';
                                                }

                                                // Page numbers (show max 5 pages)
                                                $start_page = max(1, $paged - 2);
                                                $end_page = min($total_pages, $start_page + 4);

                                                if ($start_page > 1) {
                                                    echo '<li class="page-item"><a class="page-link" href="' . esc_url($dashboard_url . 'my-courses?tab=' . $active_tab . '&current_page=1') . '">1</a></li>';
                                                    if ($start_page > 2) {
                                                        echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                                    }
                                                }

                                                for ($i = $start_page; $i <= $end_page; $i++) {
                                                    $active_class = ($i == $paged) ? ' active' : '';
                                                    echo '<li class="page-item' . $active_class . '"><a class="page-link" href="' . esc_url($dashboard_url . 'my-courses?tab=' . $active_tab . '&current_page=' . $i) . '">' . $i . '</a></li>';
                                                }

                                                if ($end_page < $total_pages) {
                                                    if ($end_page < $total_pages - 1) {
                                                        echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                                                    }
                                                    echo '<li class="page-item"><a class="page-link" href="' . esc_url($dashboard_url . 'my-courses?tab=' . $active_tab . '&current_page=' . $total_pages) . '">' . $total_pages . '</a></li>';
                                                }

                                                // Next button
                                                if ($paged < $total_pages) {
                                                    echo '<li class="page-item"><a class="page-link" href="' . esc_url($dashboard_url . 'my-courses?tab=' . $active_tab . '&current_page=' . ($paged + 1)) . '">' . esc_html__('Sonraki', 'marketking-multivendor-marketplace-for-woocommerce') . ' <em class="icon ni ni-chevron-right"></em></a></li>';
                                                } else {
                                                    echo '<li class="page-item disabled"><span class="page-link">' . esc_html__('Sonraki', 'marketking-multivendor-marketplace-for-woocommerce') . ' <em class="icon ni ni-chevron-right"></em></span></li>';
                                                }

                                                echo '</ul>';
                                                echo '</nav>';
                                            }
                                            ?>
                                        </div>
                                        <?php endif; ?>
                                        <?php
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>




                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modals -->
<?php
// Modal'ları ayrı bir döngüde oluştur
if (!empty($results)) {
    foreach ($results as $post) :
        setup_postdata($post);
?>
<div id="tutor_my_courses_delete_<?php echo esc_attr($post->ID); ?>" class="tutor-modal" style="display: none;">
    <div class="tutor-modal-overlay"></div>
    <div class="tutor-modal-window">
        <div class="tutor-modal-content tutor-modal-content-white">
            <button class="tutor-iconic-btn tutor-modal-close-o" data-tutor-modal-close>
                <span class="tutor-icon-times" area-hidden="true">&times;</span>
            </button>

            <div class="tutor-modal-body tutor-text-center">
                <div class="tutor-mt-48">
                    <em class="icon ni ni-trash text-danger" style="font-size: 3rem;"></em>
                </div>

                <div class="tutor-fs-3 tutor-fw-medium tutor-color-black tutor-mb-12">
                    <?php esc_html_e('Bu Kursu Sil?', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                </div>
                <div class="tutor-fs-6 tutor-color-muted">
                    <strong><?php echo esc_html(get_the_title()); ?></strong><br>
                    <?php esc_html_e('Bu kursu kalıcı olarak silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                </div>

                <div class="tutor-d-flex tutor-justify-center tutor-my-48">
                    <button data-tutor-modal-close class="tutor-btn tutor-btn-outline-primary">
                        <?php esc_html_e('İptal', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </button>
                    <button class="tutor-btn tutor-btn-primary tutor-list-ajax-action tutor-ml-20"
                            data-request_data='{"course_id":<?php echo esc_attr($post->ID); ?>,"action":"marketking_delete_dashboard_course","redirect_to":"<?php echo esc_url(tutor_utils()->get_current_url()); ?>"}'
                            data-delete_element_id="tutor-dashboard-my-course-<?php echo esc_attr($post->ID); ?>">
                        <?php esc_html_e('Evet, Sil', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
    endforeach;
    wp_reset_postdata();
}
?>

<!-- Tutor-style Delete Modal for MarketKing -->
<div id="marketking_course_delete_modal" class="tutor-modal">
    <div class="tutor-modal-overlay"></div>
    <div class="tutor-modal-window">
        <div class="tutor-modal-content tutor-modal-content-white">
            <button class="tutor-iconic-btn tutor-modal-close-o" data-tutor-modal-close>
                <span class="tutor-icon-times" area-hidden="true"></span>
            </button>

            <div class="tutor-modal-body tutor-text-center">
                <div class="tutor-mt-48">
                    <img class="tutor-d-inline-block" src="<?php echo esc_attr(tutor()->url); ?>assets/images/icon-trash.svg" />
                </div>

                <div class="tutor-fs-3 tutor-fw-medium tutor-color-black tutor-mb-12">
                    <?php esc_html_e('Bu Kursu Silinsin mi?', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                </div>
                <div class="tutor-fs-6 tutor-color-muted">
                    <?php esc_html_e('Bu kursu siteden kalıcı olarak silmek istediğinizden emin misiniz? Lütfen seçiminizi onaylayın.', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                </div>

                <div class="tutor-d-flex tutor-justify-center tutor-my-48">
                    <button data-tutor-modal-close class="tutor-btn tutor-btn-outline-primary">
                        <?php esc_html_e('İptal', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </button>
                    <button id="marketking_confirm_delete_course" class="tutor-btn tutor-btn-primary tutor-list-ajax-action tutor-ml-20"
                            data-request_data='{"course_id":"","action":"marketking_delete_dashboard_course","redirect_to":"<?php echo esc_url(tutor_utils()->get_current_url()); ?>"}'
                            data-delete_element_id="">
                        <?php esc_html_e('Evet, Sil', 'marketking-multivendor-marketplace-for-woocommerce'); ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- MarketKing Course Management - Using Tutor's Native System -->
<?php
// Ensure Tutor's modal styles are loaded
if (function_exists('tutor') && !wp_style_is('tutor-frontend', 'enqueued')) {
    wp_enqueue_style('tutor-frontend', tutor()->url . 'assets/css/tutor-front.min.css', array(), TUTOR_VERSION);
}
?>

<script>
jQuery(document).ready(function($) {
    console.log('MarketKing: Using Tutor native course delete system');

    // Debug: Check if Tutor's JavaScript is loaded
    console.log('_tutorobject available:', typeof _tutorobject !== 'undefined');
    if (typeof _tutorobject !== 'undefined') {
        console.log('_tutorobject:', _tutorobject);
        console.log('Tutor nonce key:', _tutorobject.nonce_key);
        console.log('Tutor nonce value:', _tutorobject[_tutorobject.nonce_key]);
    }

    // MarketKing: Handle course delete button click to show Tutor-style modal
    $(document).on('click', '.marketking_delete_course_button', function(e) {
        e.preventDefault();

        var courseId = $(this).attr('value');
        var courseElement = $(this).closest('.tutor-card, .tutor-mycourse-' + courseId);
        var deleteElementId = courseElement.attr('id') || 'tutor-dashboard-my-course-' + courseId;

        console.log('MarketKing: Course delete button clicked for course ID:', courseId);

        // Update modal data
        var modal = $('#marketking_course_delete_modal');
        var confirmButton = modal.find('#marketking_confirm_delete_course');

        // Update the confirm button's data attributes
        var requestData = {
            course_id: courseId,
            action: 'marketking_delete_dashboard_course',
            redirect_to: window.location.href.split('?')[0] // Remove query parameters to stay on courses page
        };

        confirmButton.attr('data-request_data', JSON.stringify(requestData));
        confirmButton.attr('data-delete_element_id', deleteElementId);

        // Show the modal using Tutor's modal system
        modal.addClass('tutor-is-active');
        $('body').addClass('tutor-modal-open');

        console.log('MarketKing: Modal opened with data:', requestData);
    });

    // MarketKing: Custom AJAX handler for course deletion (Tutor compatible)
    $(document).on('click', '.tutor-list-ajax-action', function(e) {
        e.preventDefault();

        console.log('MarketKing: tutor-list-ajax-action clicked');

        var $button = $(this);
        var requestData = $button.data('request_data');
        var deleteElementId = $button.data('delete_element_id');

        console.log('Button data:', $button.data());
        console.log('Request data:', requestData);
        console.log('Delete element ID:', deleteElementId);

        if (!requestData) {
            console.error('No request data found');
            return;
        }

        // Parse request data if it's a string
        if (typeof requestData === 'string') {
            try {
                requestData = JSON.parse(requestData);
            } catch (e) {
                console.error('Failed to parse request data:', e);
                return;
            }
        }

        console.log('Parsed request data:', requestData);

        // Check if this is a course deletion
        if (requestData.action === 'marketking_delete_dashboard_course') {

            // Modal should already be open, no need for additional confirm
            // The modal itself handles the confirmation

            // Disable button
            var originalText = $button.text();
            $button.prop('disabled', true).text('Siliniyor...');

            // Prepare AJAX data exactly like Tutor does
            var ajaxData = {
                action: requestData.action,
                course_id: requestData.course_id
            };

            // Add Tutor nonce exactly like Tutor does
            if (typeof _tutorobject !== 'undefined' && _tutorobject.nonce_key) {
                ajaxData[_tutorobject.nonce_key] = _tutorobject[_tutorobject.nonce_key];
            }

            console.log('AJAX data to send:', ajaxData);

            // Send AJAX request
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: ajaxData,
                success: function(response) {
                    console.log('AJAX Success:', response);

                    if (response.success) {
                        // Close modal exactly like Tutor does
                        $button.closest('.tutor-modal').hide();
                        $('body').removeClass('tutor-modal-open');

                        // Remove course element exactly like Tutor does
                        if (deleteElementId) {
                            $('#' + deleteElementId).fadeOut(300, function() {
                                $(this).remove();
                            });
                        }

                        // Show success message
                        if (response.data && response.data.message) {
                            alert(response.data.message);
                        } else if (typeof response === 'string') {
                            alert(response);
                        } else {
                            alert('Kurs başarıyla silindi!');
                        }

                        // Stay on courses page after deletion - just reload the page
                        setTimeout(function() {
                            console.log('MarketKing: Reloading courses page after successful deletion');
                            window.location.reload();
                        }, 1000);

                    } else {
                        var errorMessage = 'Bilinmeyen hata';
                        if (response.data && response.data.message) {
                            errorMessage = response.data.message;
                        } else if (typeof response === 'string') {
                            errorMessage = response;
                        }
                        alert('Hata: ' + errorMessage);
                        $button.prop('disabled', false).text(originalText);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error, xhr.responseText);
                    alert('Sunucu hatası: ' + error);
                    $button.prop('disabled', false).text(originalText);
                }
            });
        }
    });
});
</script>

<style>
.tutor-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: none;
}

.tutor-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.tutor-modal-window {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 20px;
}

.tutor-modal-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 100%;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.tutor-modal-close-o {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    z-index: 1;
}

.tutor-modal-close-o:hover {
    color: #000;
}

.tutor-modal-body {
    padding: 40px 30px;
}

.tutor-mt-48 {
    margin-top: 48px;
}

.tutor-mb-12 {
    margin-bottom: 12px;
}

.tutor-my-48 {
    margin: 48px 0;
}

.tutor-ml-20 {
    margin-left: 20px;
}

.tutor-fs-3 {
    font-size: 1.5rem;
}

.tutor-fs-6 {
    font-size: 1rem;
}

.tutor-fw-medium {
    font-weight: 500;
}

.tutor-color-black {
    color: #000;
}

.tutor-color-muted {
    color: #666;
}

.tutor-text-center {
    text-align: center;
}

.tutor-d-flex {
    display: flex;
}

.tutor-d-inline-block {
    display: inline-block;
}

.tutor-justify-center {
    justify-content: center;
}

.tutor-btn {
    padding: 10px 20px;
    border-radius: 4px;
    border: 1px solid;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    font-weight: 500;
}

.tutor-btn-primary {
    background: #007cba;
    color: white;
    border-color: #007cba;
}

.tutor-btn-primary:hover {
    background: #005a87;
    border-color: #005a87;
}

.tutor-btn-outline-primary {
    background: transparent;
    color: #007cba;
    border-color: #007cba;
}

.tutor-btn-outline-primary:hover {
    background: #007cba;
    color: white;
}
</style>